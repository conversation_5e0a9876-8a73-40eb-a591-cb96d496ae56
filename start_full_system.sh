#!/bin/bash
# 完整系统启动脚本（前端 + AI集成后端）- 自动监控日志版本

set -e  # 遇到错误时退出

# 函数：检查端口是否在使用
check_port_in_use() {
    local port=$1
    local service_name=$2
    local timeout=$3 # 超时时间，秒
    echo "🔍 检查 $service_name 服务端口 $port 是否可用..."
    for i in $(seq 1 $timeout); do
        if ! lsof -i :$port > /dev/null 2>&1; then
            echo "✅ $service_name 服务端口 $port 可用。"
            return 0
        fi
        echo "   端口 $port 仍在占用，等待 $i/$timeout 秒..."
        sleep 1
    done
    echo "❌ 错误：$service_name 服务端口 $port 在 $timeout 秒内仍被占用，无法启动。请手动关闭占用该端口的进程。"
    return 1
}

echo "🚀 启动AI金融分析完整系统 (自动监控日志)"
echo "================================"

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "backend" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 1. 检查环境变量
echo "🔧 检查环境变量..."
if [ ! -f ".env" ]; then
    echo "⚠️  未找到.env文件，请先运行 ./setup_environment.sh 配置环境变量"
    exit 1
fi

# 加载环境变量
set -a  # 自动导出变量
source .env
set +a

# 检查必要的环境变量
if [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ GEMINI_API_KEY未设置，请检查.env文件"
    exit 1
fi

if [ -z "$TUSHARE_TOKEN" ]; then
    echo "⚠️  TUSHARE_TOKEN未设置，数据功能可能受限"
fi

echo "✅ 环境变量检查完成"

# 2. 检查Python虚拟环境
echo "🐍 检查Python虚拟环境..."
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
echo "✅ Python虚拟环境已激活"

# 3. 安装Python依赖
echo "📦 安装Python依赖..."
pip install -q -r requirements.txt
echo "✅ Python依赖安装完成"

# 4. 检查Node.js依赖（如果有前端）
if [ -d "frontend" ]; then
    echo "📦 检查前端依赖..."
    cd frontend
    
    # 确保依赖是最新的
    echo "安装/更新前端依赖..."
    npm ci
    
    cd ..
    echo "✅ 前端依赖检查完成"
fi

# 5. 启动后端服务（后台运行）
echo "🎯 启动AI集成后端服务..."
cd backend

# 停止可能已运行的服务
pkill -f "python server.py" || true
pkill -f "uvicorn" || true
pkill -f "fastapi" || true
sleep 2

# 检查后端端口是否可用
if ! check_port_in_use "8001" "后端" "10"; then
    exit 1
fi

# 启动后端服务
nohup python server.py --host 0.0.0.0 --port 8001 > ../backend.log 2>&1 &
BACKEND_PID=$!

cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8001/health > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ 后端服务启动超时"
        kill $BACKEND_PID || true
        exit 1
    fi
    
    sleep 1
done

# 6. 启动前端服务（如果存在）
if [ -d "frontend" ]; then
    echo "🌐 启动前端服务..."
    cd frontend
    
    # 停止可能已运行的前端服务
    pkill -f "npm run dev" || true
    pkill -f "next dev" || true
    pkill -f "node" || true
    sleep 2
    
    # 检查前端端口是否可用
    if ! check_port_in_use "3000" "前端" "10"; then
        exit 1
    fi
    
    # 启动前端服务
    nohup npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    cd ..
    
    # 等待前端启动
    echo "⏳ 等待前端服务启动..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
            break
        fi
        
        if [ $i -eq 60 ]; then
            echo "⚠️  前端服务启动可能有问题，请检查 frontend.log"
            break
        fi
        
        sleep 2
    done
fi

# 7. 保存PID到文件以便后续停止
echo "$BACKEND_PID" > .backend.pid
if [ -d "frontend" ]; then
    echo "$FRONTEND_PID" > .frontend.pid
fi

# 8. 显示启动信息
echo ""
echo "🎉 系统启动完成！"
echo "================================"
echo "📊 后端服务:"
echo "   - API文档: http://localhost:8001/docs"
echo "   - AI聊天: http://localhost:8001/chat/stream"
echo "   - 健康检查: http://localhost:8001/health"
echo "   - 进程ID: $BACKEND_PID"

if [ -d "frontend" ]; then
    echo ""
    echo "🌐 前端服务:"
    echo "   - 用户界面: http://localhost:3000"
    echo "   - 进程ID: $FRONTEND_PID"
fi

echo ""
echo "🛑 停止服务: ./stop_full_system.sh"
echo ""

# 9. 自动开始监控后端日志
echo "📊 自动开始监控后端日志..."
echo "💡 提示：按 Ctrl+C 可以停止日志监控并返回终端"
echo "💡 服务将继续在后台运行，使用 ./stop_full_system.sh 停止"
echo "================================"
sleep 2

# 开始监控日志
tail -f backend.log 