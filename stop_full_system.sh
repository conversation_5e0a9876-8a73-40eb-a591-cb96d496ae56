#!/bin/bash
# 停止完整系统脚本

# 函数：检查端口是否在使用
check_port_in_use() {
    local port=$1
    local service_name=$2
    echo "🔍 检查 $service_name 服务端口 $port 是否已释放..."
    for i in {1..10}; do # 最多等待10秒
        if ! lsof -i :$port > /dev/null 2>&1; then
            echo "✅ $service_name 服务端口 $port 已成功释放。" 
            return 0
        fi
        sleep 1
    done
    echo "❌ 警告：$service_name 服务端口 $port 仍在占用，可能需要手动干预。" 
    return 1
}

echo "🛑 停止AI金融分析系统"
echo "===================="

# 函数：停止指定PID文件中的进程
stop_process_by_pid() {
    local name=$1
    local pid_file=$2
    local pkill_patterns=$3
    local port_to_check=$4 # 新增参数：要检查的端口

    echo "🔄 尝试停止 $name 服务..."

    # 1. 尝试通过PID文件停止
    if [ -f "$pid_file" ]; then
        PID=$(cat "$pid_file")
        if [ -n "$PID" ] && ps -p $PID > /dev/null 2>&1; then
            echo "🎯 从PID文件停止 $name 服务 (PID: $PID)..."
            kill $PID
            sleep 3 # 给予更多时间让进程自行终止

            if ps -p $PID > /dev/null 2>&1; then
                echo "强制停止 $name 服务 (PID: $PID)..."
                kill -9 $PID
                sleep 2 # 增加等待时间
            fi
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "✅ $name 服务 (PID: $PID) 已停止"
            else
                echo "❌ 警告：$name 服务 (PID: $PID) 可能未能完全停止，请手动检查。" 
            fi
        else
            echo "⚠️   $name 服务PID文件存在但进程未运行或PID无效。" 
        fi
        rm -f "$pid_file" # 无论如何都移除PID文件
    else
        echo "⚠️  未找到 $name 的PID文件。" 
    fi

    # 2. 尝试通过进程名强制停止 (兜底机制)
    echo "🧹 尝试通过进程名清理 $name 进程..."
    local any_killed=false
    for pattern in $pkill_patterns; do
        if pgrep -f "$pattern" > /dev/null; then
            echo "   强制停止匹配 '$pattern' 的 $name 进程..."
            pkill -9 -f "$pattern"
            sleep 1
            if pgrep -f "$pattern" > /dev/null; then
                echo "   ❌ 警告：匹配 '$pattern' 的 $name 进程仍在运行，可能需要手动干预。" 
            else
                echo "   ✅ 匹配 '$pattern' 的 $name 进程已停止"
                any_killed=true
            fi
        else
            echo "   未找到匹配 '$pattern' 的 $name 进程." 
        fi
    done

    if [ "$any_killed" = true ]; then
        echo "✅ $name 进程清理完成。" 
    elif [ ! -f "$pid_file" ] && ! pgrep -f "${pkill_patterns[0]}" > /dev/null; then
        # 如果PID文件不存在，且第一个pkill模式也没有找到，则认为已经停止
        echo "ℹ️ $name 服务似乎未在运行或已停止。" 
    fi

    # 3. 检查端口是否已释放
    if [ -n "$port_to_check" ]; then
        check_port_in_use "$port_to_check" "$name"
    fi
    echo ""
}

# 停止后端服务
# 考虑到uvicorn通常是启动Python FastAPI应用的方式
stop_process_by_pid "后端" ".backend.pid" "'uvicorn backend.server:app' 'python server.py'" "8001"

# 停止前端服务
# npm run dev 可能会启动多个子进程，next dev是其主要工作进程
stop_process_by_pid "前端" ".frontend.pid" "'npm run dev' 'next dev' 'node'" "3000"

# 最终检查并清理所有可能残留的通用进程
echo "🔍 最终检查并清理所有可能残留的通用进程..."
PROCESSES_TO_CHECK=("uvicorn" "fastapi" "python server.py" "npm run dev" "next dev" "node")
ALL_CLEAN=true
for proc in "${PROCESSES_TO_CHECK[@]}"; do
    if pgrep -f "$proc" > /dev/null; then
        echo "❌ 发现残留进程: $proc - 尝试再次强制停止..." 
        pkill -9 -f "$proc" || true
        sleep 1
        if pgrep -f "$proc" > /dev/null; then
            echo "   ⚠️  警告：进程 $proc 仍然存在，请手动检查。" 
            ALL_CLEAN=false
        else
            echo "   ✅ 进程 $proc 已清除。" 
        fi
    fi
done

if [ "$ALL_CLEAN" = true ]; then
    echo "✅ 所有已知相关进程已成功停止并清理。" 
else
    echo "❌ 警告：部分进程可能未能完全停止，请手动检查系统进程。" 
fi

echo ""
echo "✅ 系统已完全停止"
echo ""
echo "📝 日志文件仍然保留："
if [ -f "backend.log" ]; then
    echo "   - backend.log (后端日志)" 
fi
if [ -f "frontend.log" ]; then
    echo "   - frontend.log (前端日志)" 
fi

echo ""
echo "🚀 要重新启动系统，请运行："
echo "   ./start_full_system.sh" 