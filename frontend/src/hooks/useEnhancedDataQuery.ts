'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import {
  EnhancedDataQueryState,
  StockQueryResult,
  NewsQueryResult,
  TechnicalIndicatorResult,
  MarketOverviewData,
  WatchlistItem,
  DataQueryPreferences
} from '@/types';
import {
  queryStockData,
  getTechnicalIndicators,
  getStockNews,
  getMarketOverview,
  manageWatchlist,
  exportData,
  getMarketStatus,
  getStockSuggestions,
  getDefaultTushareToken,
  StockDataRequest,
  TechnicalIndicatorRequest,
  NewsRequest,
  WatchlistRequest,
  DataExportRequest
} from '@/utils/api';

const defaultPreferences: DataQueryPreferences = {
  default_period: '1y',
  auto_refresh: false,
  refresh_interval: 300000, // 5 minutes
  default_indicators: ['sma', 'ema', 'rsi', 'macd'],
  news_sources: ['eastmoney', 'caixin'],
  show_charts: true,
  chart_type: 'candlestick'
};

const initialState: EnhancedDataQueryState = {
  activeTab: 'stock-data',
  stockData: null,
  newsData: null,
  technicalData: null,
  marketOverview: null,
  watchlist: [],
  preferences: defaultPreferences,
  loading: {
    stock: false,
    news: false,
    technical: false,
    market: false
  },
  errors: {
    stock: null,
    news: null,
    technical: null,
    market: null
  }
};

export const useEnhancedDataQuery = () => {
  const [state, setState] = useState<EnhancedDataQueryState>(initialState);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [currentSymbol, setCurrentSymbol] = useState<string>('');
  const [searchSuggestions, setSearchSuggestions] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Update specific parts of state
  const updateState = useCallback((updates: Partial<EnhancedDataQueryState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const updateLoading = useCallback((key: keyof EnhancedDataQueryState['loading'], value: boolean) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, [key]: value }
    }));
  }, []);

  const updateError = useCallback((key: keyof EnhancedDataQueryState['errors'], value: string | null) => {
    setState(prev => ({
      ...prev,
      errors: { ...prev.errors, [key]: value }
    }));
  }, []);

  // Stock data query
  const queryStock = useCallback(async (symbol: string, period?: string): Promise<StockQueryResult | null> => {
    if (!symbol.trim()) return null;

    updateLoading('stock', true);
    updateError('stock', null);

    try {
      const request: StockDataRequest = {
        symbol: symbol.trim().toUpperCase(),
        period: period || state.preferences.default_period,
        tushare_token: getDefaultTushareToken()
      };

      const result = await queryStockData(request);
      
      setState(prev => ({
        ...prev,
        stockData: result
      }));

      setCurrentSymbol(symbol.trim().toUpperCase());
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to query stock data';
      updateError('stock', errorMessage);
      throw error;
    } finally {
      updateLoading('stock', false);
    }
  }, [state.preferences.default_period]);

  // Technical indicators query
  const queryTechnicalIndicators = useCallback(async (symbol: string, indicators?: string[], period?: string): Promise<TechnicalIndicatorResult | null> => {
    if (!symbol.trim()) return null;

    updateLoading('technical', true);
    updateError('technical', null);

    try {
      const request: TechnicalIndicatorRequest = {
        symbol: symbol.trim().toUpperCase(),
        indicators: indicators || state.preferences.default_indicators,
        period: period || state.preferences.default_period,
        tushare_token: getDefaultTushareToken()
      };

      const result = await getTechnicalIndicators(request);
      
      setState(prev => ({
        ...prev,
        technicalData: result
      }));

      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to query technical indicators';
      updateError('technical', errorMessage);
      throw error;
    } finally {
      updateLoading('technical', false);
    }
  }, [state.preferences.default_indicators, state.preferences.default_period]);

  // News query
  const queryNews = useCallback(async (symbol: string, options?: Partial<NewsRequest>): Promise<NewsQueryResult | null> => {
    if (!symbol.trim()) return null;

    updateLoading('news', true);
    updateError('news', null);

    try {
      const request: NewsRequest = {
        symbol: symbol.trim().toUpperCase(),
        include_general_news: options?.include_general_news ?? true,
        date_for_reports: options?.date_for_reports,
        max_rows_per_source: options?.max_rows_per_source ?? 30
      };

      const result = await getStockNews(request);
      
      setState(prev => ({
        ...prev,
        newsData: result
      }));

      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to query stock news';
      updateError('news', errorMessage);
      throw error;
    } finally {
      updateLoading('news', false);
    }
  }, []);

  // Market overview query
  const queryMarketOverview = useCallback(async (market?: string): Promise<MarketOverviewData | null> => {
    updateLoading('market', true);
    updateError('market', null);

    try {
      const result = await getMarketOverview(market);
      
      setState(prev => ({
        ...prev,
        marketOverview: result
      }));

      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to query market overview';
      updateError('market', errorMessage);
      throw error;
    } finally {
      updateLoading('market', false);
    }
  }, []);

  // Watchlist management
  const addToWatchlist = useCallback(async (symbol: string): Promise<void> => {
    try {
      const request: WatchlistRequest = {
        action: 'add',
        symbol: symbol.trim().toUpperCase(),
        alerts_enabled: true
      };

      await manageWatchlist(request);
      
      // Refresh watchlist
      const listRequest: WatchlistRequest = { action: 'list' };
      const result = await manageWatchlist(listRequest);
      
      setState(prev => ({
        ...prev,
        watchlist: result.watchlist || []
      }));
    } catch (error: any) {
      throw new Error(error.message || 'Failed to add to watchlist');
    }
  }, []);

  const removeFromWatchlist = useCallback(async (symbol: string): Promise<void> => {
    try {
      const request: WatchlistRequest = {
        action: 'remove',
        symbol: symbol.trim().toUpperCase()
      };

      await manageWatchlist(request);
      
      setState(prev => ({
        ...prev,
        watchlist: prev.watchlist.filter(item => item.symbol !== symbol.trim().toUpperCase())
      }));
    } catch (error: any) {
      throw new Error(error.message || 'Failed to remove from watchlist');
    }
  }, []);

  const updateWatchlistItem = useCallback(async (symbol: string, updates: Partial<WatchlistItem>): Promise<void> => {
    try {
      const request: WatchlistRequest = {
        action: 'update',
        symbol: symbol.trim().toUpperCase(),
        target_price: updates.target_price,
        stop_loss: updates.stop_loss,
        alerts_enabled: updates.alerts_enabled
      };

      await manageWatchlist(request);
      
      setState(prev => ({
        ...prev,
        watchlist: prev.watchlist.map(item => 
          item.symbol === symbol.trim().toUpperCase() 
            ? { ...item, ...updates }
            : item
        )
      }));
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update watchlist item');
    }
  }, []);

  // Data export
  const exportStockData = useCallback(async (symbol: string, format: 'csv' | 'json', type: 'stock-data' | 'news' | 'technical-indicators'): Promise<void> => {
    try {
      const request: DataExportRequest = {
        type,
        symbol: symbol.trim().toUpperCase(),
        format,
        period: state.preferences.default_period,
        indicators: state.preferences.default_indicators
      };

      const blob = await exportData(request);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `${symbol}_${type}_data.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to export data');
    }
  }, [state.preferences.default_period, state.preferences.default_indicators]);

  // Search suggestions
  const searchSymbols = useCallback(async (query: string): Promise<void> => {
    if (!query.trim()) {
      setSearchSuggestions([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await getStockSuggestions(query.trim(), 10);
      setSearchSuggestions(response.suggestions || []);
    } catch (error) {
      console.error('Search failed:', error);
      setSearchSuggestions([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Comprehensive data refresh
  const refreshAllData = useCallback(async (symbol?: string): Promise<void> => {
    const targetSymbol = symbol || currentSymbol;
    if (!targetSymbol) return;

    try {
      // Query all data types in parallel
      const promises = [];
      
      if (state.activeTab === 'stock-data' || state.activeTab === 'technical-analysis') {
        promises.push(queryStock(targetSymbol));
      }
      
      if (state.activeTab === 'technical-analysis') {
        promises.push(queryTechnicalIndicators(targetSymbol));
      }
      
      if (state.activeTab === 'market-news') {
        promises.push(queryNews(targetSymbol));
      }
      
      if (state.activeTab === 'market-overview') {
        promises.push(queryMarketOverview());
      }

      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  }, [currentSymbol, state.activeTab, queryStock, queryTechnicalIndicators, queryNews, queryMarketOverview]);

  // Auto-refresh setup
  useEffect(() => {
    if (state.preferences.auto_refresh && currentSymbol) {
      refreshIntervalRef.current = setInterval(() => {
        refreshAllData();
      }, state.preferences.refresh_interval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [state.preferences.auto_refresh, state.preferences.refresh_interval, currentSymbol, refreshAllData]);

  // Update preferences
  const updatePreferences = useCallback((updates: Partial<DataQueryPreferences>): void => {
    setState(prev => ({
      ...prev,
      preferences: { ...prev.preferences, ...updates }
    }));
  }, []);

  // Set active tab
  const setActiveTab = useCallback((tab: EnhancedDataQueryState['activeTab']): void => {
    setState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  // Load watchlist
  const loadWatchlist = useCallback(async (): Promise<void> => {
    try {
      const request: WatchlistRequest = { action: 'list' };
      const result = await manageWatchlist(request);
      
      setState(prev => ({
        ...prev,
        watchlist: result.watchlist || []
      }));
    } catch (error) {
      console.error('Failed to load watchlist:', error);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadWatchlist();
  }, [loadWatchlist]);

  return {
    // State
    ...state,
    currentSymbol,
    searchSuggestions,
    isSearching,

    // Actions
    queryStock,
    queryTechnicalIndicators,
    queryNews,
    queryMarketOverview,
    addToWatchlist,
    removeFromWatchlist,
    updateWatchlistItem,
    exportStockData,
    searchSymbols,
    refreshAllData,
    updatePreferences,
    setActiveTab,
    loadWatchlist,

    // Utilities
    isInWatchlist: (symbol: string) => state.watchlist.some(item => item.symbol === symbol.trim().toUpperCase()),
    hasData: (type: keyof Pick<EnhancedDataQueryState, 'stockData' | 'newsData' | 'technicalData' | 'marketOverview'>) => !!state[type],
    isLoading: Object.values(state.loading).some(loading => loading),
    hasErrors: Object.values(state.errors).some(error => error !== null)
  };
}; 