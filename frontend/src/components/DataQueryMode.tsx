'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AppMode } from '@/types';
import { useEnhancedDataQuery } from '@/hooks/useEnhancedDataQuery';
import LoadingSpinner from './LoadingSpinner';
import EnhancedChartContainer from './EnhancedChartContainer';
import { DotIndicator, BadgeIndicator, StatusIndicator } from './VisualIndicator';
import EnhancedButton from './EnhancedButton';
import CircularProgress from './CircularProgress';

interface DataQueryModeProps {
  setMode: React.Dispatch<React.SetStateAction<AppMode>>;
}

const DataQueryMode: React.FC<DataQueryModeProps> = ({ setMode }) => {
  const {
    // State
    activeTab,
    stockData,
    newsData,
    technicalData,
    marketOverview,
    watchlist,
    preferences,
    loading,
    errors,
    currentSymbol,
    searchSuggestions,
    isSearching,

    // Actions
    queryStock,
    queryTechnicalIndicators,
    queryNews,
    queryMarketOverview,
    addToWatchlist,
    removeFromWatchlist,
    updateWatchlistItem,
    exportStockData,
    searchSymbols,
    refreshAllData,
    updatePreferences,
    setActiveTab,

    // Utilities
    isInWatchlist,
    hasData,
    isLoading,
    hasErrors
  } = useEnhancedDataQuery();

  const [searchQuery, setSearchQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState(preferences.default_period);
  const [selectedIndicators, setSelectedIndicators] = useState<string[]>(preferences.default_indicators);
  const [showExportMenu, setShowExportMenu] = useState(false);

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim() && searchQuery.length > 1) {
        searchSymbols(searchQuery);
        setShowSuggestions(true);
      } else {
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, searchSymbols]);

  // Handle symbol selection
  const handleSymbolSelect = useCallback(async (symbol: string) => {
    // Removed setSearchQuery(symbol) as it's handled by onMouseDown
    // Removed setShowSuggestions(false) as it's handled by onMouseDown or onBlur
    
    // Query based on active tab
    switch (activeTab) {
      case 'stock-data':
        await queryStock(symbol, selectedPeriod);
        break;
      case 'market-news':
        await queryNews(symbol);
        break;
      case 'technical-analysis':
        await Promise.all([
          queryStock(symbol, selectedPeriod),
          queryTechnicalIndicators(symbol, selectedIndicators, selectedPeriod)
        ]);
        break;
      case 'market-overview':
        await queryMarketOverview();
        break;
    }
  }, [activeTab, selectedPeriod, selectedIndicators, queryStock, queryNews, queryTechnicalIndicators, queryMarketOverview]);

  // Handle tab change
  const handleTabChange = useCallback((tab: typeof activeTab) => {
    setActiveTab(tab);
    
    // Auto-load data for market overview
    if (tab === 'market-overview') {
      queryMarketOverview();
    }
  }, [setActiveTab, queryMarketOverview]);

  // Format price change
  const formatPriceChange = useCallback((change: number, showSign: boolean = true) => {
    const sign = change >= 0 ? '+' : '';
    const formatted = change.toFixed(2);
    return showSign ? `${sign}${formatted}%` : `${formatted}%`;
  }, []);

  // Format large numbers
  const formatNumber = useCallback((num: number) => {
    if (num >= 1000000000) return (num / 1000000000).toFixed(2) + 'B';
    if (num >= 1000000) return (num / 1000000).toFixed(2) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toFixed(2);
  }, []);

  // Get change color class
  const getChangeColorClass = useCallback((change: number) => {
    return change >= 0 ? 'text-success' : 'text-error';
  }, []);

  // Tabs configuration
  const tabs = useMemo(() => [
    { 
      id: 'stock-data' as const, 
      label: '股票数据', 
      icon: <DotIndicator category="data" size="sm" />,
      description: '查询股票价格、成交量等基础数据'
    },
    { 
      id: 'market-news' as const, 
      label: '市场资讯', 
      icon: <DotIndicator category="analysis" size="sm" />,
      description: '获取股票新闻、财报发布等资讯'
    },
    { 
      id: 'technical-analysis' as const, 
      label: '技术分析', 
      icon: <DotIndicator category="chart" size="sm" />,
      description: '计算技术指标、图表分析'
    },
    { 
      id: 'market-overview' as const, 
      label: '市场概览', 
      icon: <DotIndicator category="research" size="sm" />,
      description: '市场指数、板块表现概况'
    }
  ], []);

  return (
    <div className="mode-container">
      {/* Header */}
      <div className="mode-header mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setMode('home')}
              className="btn-base btn-secondary flex items-center space-x-2"
            >
              <span>←</span>
              <span>返回首页</span>
            </button>
            <div>
              <h1 className="text-heading-xl flex items-center space-x-3">
                <DotIndicator category="data" size="lg" />
                <span>数据查询中心</span>
              </h1>
              <p className="text-secondary mt-1">
                全面的股票数据查询、新闻资讯和技术分析工具
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Auto-refresh toggle */}
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.auto_refresh}
                onChange={(e) => updatePreferences({ auto_refresh: e.target.checked })}
                className="rounded border-primary"
              />
              <span className="text-sm text-secondary">自动刷新</span>
            </label>
            
            {/* Refresh button */}
            <EnhancedButton
              variant="secondary"
              size="sm"
              onClick={() => refreshAllData()}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
                             {isLoading ? (
                 <CircularProgress progress={50} size={16} />
               ) : (
                 <DotIndicator category="navigation" size="xs" />
               )}
               <span>刷新</span>
             </EnhancedButton>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mb-8">
        <div className="border-b border-primary">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`flex items-center space-x-2 py-3 px-1 border-b-2 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'border-brand-primary text-brand-primary'
                    : 'border-transparent text-secondary hover:text-primary hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
                                 {loading[tab.id === 'stock-data' ? 'stock' : tab.id === 'market-news' ? 'news' : tab.id === 'technical-analysis' ? 'technical' : 'market'] && (
                   <CircularProgress progress={50} size={12} />
                 )}
              </button>
            ))}
          </nav>
        </div>
        <div className="mt-2">
          <p className="text-sm text-tertiary">
            {tabs.find(tab => tab.id === activeTab)?.description}
          </p>
        </div>
      </div>

      {/* Search Section */}
      {activeTab !== 'market-overview' && (
        <div className="mb-8">
          <div className="card-base p-6">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <label className="block text-sm font-medium text-primary mb-2">
                  股票代码搜索
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setShowSuggestions(searchQuery.length > 1)}
                  onBlur={() => setShowSuggestions(false)}
                  placeholder="输入股票代码或名称，如 AAPL、000001"
                  className="input-base w-full"
                />
                
                {/* Search suggestions dropdown */}
                {showSuggestions && searchSuggestions.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-secondary rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {searchSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onMouseDown={(e) => {
                          e.preventDefault();
                          const selectedSymbol = suggestion.symbol || suggestion.ts_code;
                          setSearchQuery(selectedSymbol);
                          handleSymbolSelect(selectedSymbol);
                          setShowSuggestions(false);
                        }}
                        className="w-full text-left px-4 py-3 hover:bg-secondary transition-colors border-b border-secondary last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-primary">
                              {suggestion.symbol || suggestion.ts_code}
                            </div>
                            <div className="text-sm text-secondary">
                              {suggestion.name}
                            </div>
                          </div>
                          <div className="text-xs text-tertiary">
                            {suggestion.market}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              {activeTab === 'stock-data' || activeTab === 'technical-analysis' ? (
                <div className="min-w-[120px]">
                  <label className="block text-sm font-medium text-primary mb-2">
                    时间周期
                  </label>
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    className="input-base"
                  >
                    <option value="1m">1个月</option>
                    <option value="3m">3个月</option>
                    <option value="6m">6个月</option>
                    <option value="1y">1年</option>
                    <option value="2y">2年</option>
                    <option value="5y">5年</option>
                  </select>
                </div>
              ) : null}
              
              <div className="min-w-[100px]">
                <label className="block text-sm font-medium text-primary mb-2">
                  &nbsp;
                </label>
                <EnhancedButton
                  variant="primary"
                  onClick={() => searchQuery && handleSymbolSelect(searchQuery)}
                  disabled={!searchQuery.trim() || isLoading}
                  className="w-full flex items-center justify-center space-x-2"
                >
                  {isLoading ? (
                    <LoadingSpinner size="sm" color="white" />
                  ) : (
                    <DotIndicator category="navigation" size="sm" />
                  )}
                  <span>查询</span>
                </EnhancedButton>
              </div>
            </div>
            
            {/* Technical indicators selection for technical analysis tab */}
            {activeTab === 'technical-analysis' && (
              <div className="mt-6 pt-6 border-t border-secondary">
                <label className="block text-sm font-medium text-primary mb-3">
                  技术指标选择
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {['sma', 'ema', 'rsi', 'macd', 'bollinger', 'stoch', 'williams', 'cci'].map((indicator) => (
                    <label key={indicator} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedIndicators.includes(indicator)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedIndicators(prev => [...prev, indicator]);
                          } else {
                            setSelectedIndicators(prev => prev.filter(i => i !== indicator));
                          }
                        }}
                        className="rounded border-primary"
                      />
                      <span className="text-sm text-secondary">{indicator.toUpperCase()}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-3">
          {activeTab === 'stock-data' && (
            <StockDataTab 
              stockData={stockData}
              loading={loading.stock}
              error={errors.stock}
              currentSymbol={currentSymbol}
              period={selectedPeriod}
              onExport={(format, type) => exportStockData(currentSymbol, format, type)}
              showExportMenu={showExportMenu}
              setShowExportMenu={setShowExportMenu}
            />
          )}
          
          {activeTab === 'market-news' && (
            <NewsTab 
              newsData={newsData}
              loading={loading.news}
              error={errors.news}
              currentSymbol={currentSymbol}
            />
          )}
          
          {activeTab === 'technical-analysis' && (
            <TechnicalAnalysisTab 
              stockData={stockData}
              technicalData={technicalData}
              loading={loading}
              errors={errors}
              currentSymbol={currentSymbol}
              selectedIndicators={selectedIndicators}
            />
          )}
          
          {activeTab === 'market-overview' && (
            <MarketOverviewTab 
              marketOverview={marketOverview}
              loading={loading.market}
              error={errors.market}
            />
          )}
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <WatchlistSidebar 
            watchlist={watchlist}
            currentSymbol={currentSymbol}
            isInWatchlist={isInWatchlist}
            addToWatchlist={addToWatchlist}
            removeFromWatchlist={removeFromWatchlist}
            updateWatchlistItem={updateWatchlistItem}
            onSymbolSelect={handleSymbolSelect}
          />
        </div>
      </div>
    </div>
  );
};

// Stock Data Tab Component
const StockDataTab: React.FC<{
  stockData: any;
  loading: boolean;
  error: string | null;
  currentSymbol: string;
  period: string;
  onExport: (format: 'csv' | 'json', type: 'stock-data') => void;
  showExportMenu: boolean;
  setShowExportMenu: (show: boolean) => void;
}> = ({ stockData, loading, error, currentSymbol, period, onExport, showExportMenu, setShowExportMenu }) => {
  const formatNumber = useCallback((num: number) => {
    if (num >= 1000000000) return (num / 1000000000).toFixed(2) + 'B';
    if (num >= 1000000) return (num / 1000000).toFixed(2) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toFixed(2);
  }, []);

  const getChangeColorClass = useCallback((change: number) => {
    return change >= 0 ? 'text-success' : 'text-error';
  }, []);

     if (loading) {
     return (
       <div className="card-base p-8 text-center">
         <CircularProgress progress={50} size={40} />
         <p className="mt-4 text-secondary">正在加载股票数据...</p>
       </div>
     );
   }

  if (error) {
    return (
      <div className="card-base p-8 text-center">
        <DotIndicator category="error" size="lg" />
        <h3 className="mt-4 text-lg font-semibold text-error">数据加载失败</h3>
        <p className="mt-2 text-secondary">{error}</p>
      </div>
    );
  }

  if (!stockData) {
    return (
      <div className="card-base p-8 text-center">
        <DotIndicator category="data" size="xl" />
        <h3 className="mt-4 text-lg font-semibold text-primary">开始查询股票数据</h3>
        <p className="mt-2 text-secondary">
          输入股票代码并选择时间周期来获取详细的股票数据和图表分析
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stock Info Card */}
      <div className="card-base p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-heading-lg flex items-center space-x-3">
              <StatusIndicator status="complete" size="sm" />
              <span>{stockData.basic_info?.name || currentSymbol}</span>
                             <BadgeIndicator category="data" label={stockData.basic_info?.market || 'N/A'} size="sm" />
            </h2>
            <p className="text-sm text-secondary mt-1">
              {stockData.basic_info?.industry || '未知行业'} • {period.toUpperCase()}
            </p>
          </div>
          
          <div className="relative">
            <EnhancedButton
              variant="secondary"
              size="sm"
              onClick={() => setShowExportMenu(!showExportMenu)}
              className="flex items-center space-x-2"
            >
              <DotIndicator category="navigation" size="xs" />
              <span>导出数据</span>
            </EnhancedButton>
            
            {showExportMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white border border-secondary rounded-lg shadow-lg z-10">
                <button
                  onClick={() => { onExport('csv', 'stock-data'); setShowExportMenu(false); }}
                  className="w-full text-left px-4 py-2 hover:bg-secondary transition-colors"
                >
                  导出为 CSV
                </button>
                <button
                  onClick={() => { onExport('json', 'stock-data'); setShowExportMenu(false); }}
                  className="w-full text-left px-4 py-2 hover:bg-secondary transition-colors border-t border-secondary"
                >
                  导出为 JSON
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center p-4 bg-secondary rounded-lg">
            <div className="text-2xl font-bold text-primary">
              ${stockData.statistics?.latest_price?.toFixed(2) || 'N/A'}
            </div>
            <div className="text-sm text-secondary">最新价格</div>
          </div>
          
          <div className="text-center p-4 bg-secondary rounded-lg">
            <div className={`text-2xl font-bold ${getChangeColorClass(stockData.statistics?.price_change || 0)}`}>
              {stockData.statistics?.price_change?.toFixed(2) || 'N/A'}%
            </div>
            <div className="text-sm text-secondary">涨跌幅</div>
          </div>
          
          <div className="text-center p-4 bg-secondary rounded-lg">
            <div className="text-2xl font-bold text-primary">
              {formatNumber(stockData.statistics?.avg_volume || 0)}
            </div>
            <div className="text-sm text-secondary">平均成交量</div>
          </div>
          
          <div className="text-center p-4 bg-secondary rounded-lg">
            <div className="text-2xl font-bold text-primary">
              {stockData.statistics?.total_records || 0}
            </div>
            <div className="text-sm text-secondary">数据记录数</div>
          </div>
        </div>

        {/* Chart */}
        {stockData.kline_data && stockData.kline_data.length > 0 && (
                    <div className="mt-6">
            <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
              <DotIndicator category="chart" size="sm" />
              <span>K线图表</span>
            </h3>
            <EnhancedChartContainer
              chartData={{
                symbol: currentSymbol,
                company_name: stockData.basic_info?.name || currentSymbol,
                current_price: stockData.statistics?.latest_price || 0,
                price_change: stockData.statistics?.price_change || 0,
                price_change_percent: stockData.statistics?.price_change || 0,
                dates: stockData.kline_data.map((item: any) => item.date) || [],
                kline_data: stockData.kline_data.map((item: any) => [item.open, item.close, item.low, item.high]) || [],
                volume_data: stockData.kline_data.map((item: any) => item.volume) || [],
                last_update: stockData.timestamp || new Date().toISOString()
              }}
              metadata={{ symbol: currentSymbol }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

// News Tab Component  
const NewsTab: React.FC<{
  newsData: any;
  loading: boolean;
  error: string | null;
  currentSymbol: string;
}> = ({ newsData, loading, error, currentSymbol }) => {
     if (loading) {
     return (
       <div className="card-base p-8 text-center">
         <CircularProgress progress={50} size={40} />
         <p className="mt-4 text-secondary">正在加载新闻资讯...</p>
       </div>
     );
   }

   if (error) {
     return (
       <div className="card-base p-8 text-center">
         <DotIndicator category="error" size="lg" />
         <h3 className="mt-4 text-lg font-semibold text-error">新闻加载失败</h3>
         <p className="mt-2 text-secondary">{error}</p>
       </div>
     );
   }

   if (!newsData) {
     return (
       <div className="card-base p-8 text-center">
         <DotIndicator category="analysis" size="xl" />
         <h3 className="mt-4 text-lg font-semibold text-primary">查询股票新闻</h3>
         <p className="mt-2 text-secondary">
           输入股票代码来获取相关的新闻资讯、财报发布等信息
         </p>
       </div>
     );
   }

   return (
     <div className="space-y-6">
       {Object.entries(newsData.data_sources || {}).map(([sourceKey, sourceData]: [string, any]) => (
         <div key={sourceKey} className="card-base p-6">
           <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
             <DotIndicator category="analysis" size="sm" />
             <span>{sourceData.source}</span>
             {sourceData.total_rows && (
               <BadgeIndicator category="data" label={`${sourceData.displayed_rows}/${sourceData.total_rows}`} size="sm" />
             )}
           </h3>
          
          {sourceData.data && sourceData.data.length > 0 ? (
            <div className="space-y-4">
              {sourceData.data.map((item: any, index: number) => (
                <div key={index} className="border-l-4 border-brand-primary pl-4 py-2">
                  <h4 className="font-semibold text-primary mb-2">
                    {item.新闻标题 || item.标题 || item.title || '无标题'}
                  </h4>
                  <p className="text-sm text-secondary mb-2">
                    {item.新闻内容 || item.摘要 || item.content || '无内容预览'}
                  </p>
                  <div className="flex items-center justify-between text-xs text-tertiary">
                    <span>{item.发布时间 || item.时间间隔 || item.timestamp || '未知时间'}</span>
                    <span>{item.文章来源 || item.来源 || item.source || '未知来源'}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-secondary">
              {sourceData.message || sourceData.error || '暂无数据'}
            </p>
          )}
        </div>
      ))}
    </div>
  );
};

// Technical Analysis Tab Component
const TechnicalAnalysisTab: React.FC<{
  stockData: any;
  technicalData: any;
  loading: any;
  errors: any;
  currentSymbol: string;
  selectedIndicators: string[];
}> = ({ stockData, technicalData, loading, errors, currentSymbol, selectedIndicators }) => {
  const isLoading = loading.stock || loading.technical;
  const hasError = errors.stock || errors.technical;

     if (isLoading) {
     return (
       <div className="card-base p-8 text-center">
         <CircularProgress progress={50} size={40} />
         <p className="mt-4 text-secondary">正在计算技术指标...</p>
       </div>
     );
   }

  if (hasError) {
    return (
      <div className="card-base p-8 text-center">
        <DotIndicator category="error" size="lg" />
        <h3 className="mt-4 text-lg font-semibold text-error">技术分析失败</h3>
        <p className="mt-2 text-secondary">{errors.stock || errors.technical}</p>
      </div>
    );
  }

  if (!stockData && !technicalData) {
    return (
      <div className="card-base p-8 text-center">
        <DotIndicator category="chart" size="xl" />
        <h3 className="mt-4 text-lg font-semibold text-primary">技术分析</h3>
        <p className="mt-2 text-secondary">
          输入股票代码并选择技术指标来进行专业的技术分析
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Chart with indicators */}
      {stockData?.kline_data && (
        <div className="card-base p-6">
          <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
            <DotIndicator category="chart" size="sm" />
            <span>技术分析图表</span>
          </h3>
          <EnhancedChartContainer
            chartData={{
              symbol: currentSymbol,
              company_name: stockData.basic_info?.name || currentSymbol,
              current_price: stockData.statistics?.latest_price || 0,
              price_change: stockData.statistics?.price_change || 0,
              price_change_percent: stockData.statistics?.price_change || 0,
              dates: stockData.kline_data.map((item: any) => item.date) || [],
              kline_data: stockData.kline_data.map((item: any) => [item.open, item.close, item.low, item.high]) || [],
              volume_data: stockData.kline_data.map((item: any) => item.volume) || [],
              last_update: stockData.timestamp || new Date().toISOString()
            }}
            metadata={{ symbol: currentSymbol }}
          />
        </div>
      )}

      {/* Technical indicators results */}
      {technicalData?.indicators && (
        <div className="card-base p-6">
          <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
            <DotIndicator category="data" size="sm" />
            <span>技术指标数值</span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(technicalData.indicators).map(([key, value]: [string, any]) => (
              <div key={key} className="p-4 bg-secondary rounded-lg">
                <h4 className="font-semibold text-primary mb-2">{key.toUpperCase()}</h4>
                <pre className="text-sm text-secondary overflow-x-auto">
                  {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                </pre>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Market Overview Tab Component
const MarketOverviewTab: React.FC<{
  marketOverview: any;
  loading: boolean;
  error: string | null;
}> = ({ marketOverview, loading, error }) => {
  if (loading) {
    return (
      <div className="card-base p-8 text-center">
                 <CircularProgress progress={50} size={40} />
         <p className="mt-4 text-secondary">正在加载市场概览...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card-base p-8 text-center">
        <DotIndicator category="error" size="lg" />
        <h3 className="mt-4 text-lg font-semibold text-error">市场数据加载失败</h3>
        <p className="mt-2 text-secondary">{error}</p>
      </div>
    );
  }

  if (!marketOverview) {
    return (
      <div className="card-base p-8 text-center">
        <DotIndicator category="research" size="xl" />
        <h3 className="mt-4 text-lg font-semibold text-primary">市场概览</h3>
        <p className="mt-2 text-secondary">
          查看市场指数、板块表现和热门股票等市场概况信息
        </p>
      </div>
    );
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000000) return (num / 1000000000).toFixed(2) + 'B';
    if (num >= 1000000) return (num / 1000000).toFixed(2) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toFixed(2);
  };

  const getChangeColorClass = (change: number) => {
    return change >= 0 ? 'text-success' : 'text-error';
  };

  return (
    <div className="space-y-6">
      {/* Market indices */}
      {marketOverview.market_indices && (
        <div className="card-base p-6">
          <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
            <DotIndicator category="research" size="sm" />
            <span>市场指数</span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {marketOverview.market_indices.map((index: any, i: number) => (
              <div key={i} className="p-4 bg-secondary rounded-lg text-center">
                <div className="text-lg font-semibold text-primary">{index.name}</div>
                <div className="text-2xl font-bold text-primary mt-2">{index.value.toFixed(2)}</div>
                <div className={`text-sm mt-1 ${getChangeColorClass(index.change_percent)}`}>
                  {index.change_percent >= 0 ? '+' : ''}{index.change_percent.toFixed(2)}%
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top gainers and losers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {marketOverview.top_gainers && (
          <div className="card-base p-6">
            <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
              <DotIndicator category="success" size="sm" />
              <span>涨幅榜</span>
            </h3>
            <div className="space-y-3">
              {marketOverview.top_gainers.slice(0, 5).map((stock: any, i: number) => (
                <div key={i} className="flex items-center justify-between py-2 border-b border-secondary last:border-b-0">
                  <div>
                    <div className="font-semibold text-primary">{stock.symbol}</div>
                    <div className="text-sm text-secondary">{stock.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-primary">${stock.price.toFixed(2)}</div>
                    <div className="text-sm text-success">+{stock.change_percent.toFixed(2)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {marketOverview.top_losers && (
          <div className="card-base p-6">
            <h3 className="text-heading-sm mb-4 flex items-center space-x-2">
              <DotIndicator category="error" size="sm" />
              <span>跌幅榜</span>
            </h3>
            <div className="space-y-3">
              {marketOverview.top_losers.slice(0, 5).map((stock: any, i: number) => (
                <div key={i} className="flex items-center justify-between py-2 border-b border-secondary last:border-b-0">
                  <div>
                    <div className="font-semibold text-primary">{stock.symbol}</div>
                    <div className="text-sm text-secondary">{stock.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-primary">${stock.price.toFixed(2)}</div>
                    <div className="text-sm text-error">{stock.change_percent.toFixed(2)}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Watchlist Sidebar Component
const WatchlistSidebar: React.FC<{
  watchlist: any[];
  currentSymbol: string;
  isInWatchlist: (symbol: string) => boolean;
  addToWatchlist: (symbol: string) => Promise<void>;
  removeFromWatchlist: (symbol: string) => Promise<void>;
  updateWatchlistItem: (symbol: string, updates: any) => Promise<void>;
  onSymbolSelect: (symbol: string) => void;
}> = ({ 
  watchlist, 
  currentSymbol, 
  isInWatchlist, 
  addToWatchlist, 
  removeFromWatchlist, 
  updateWatchlistItem,
  onSymbolSelect 
}) => {
  const [isManaging, setIsManaging] = useState(false);

  const handleWatchlistToggle = async () => {
    if (!currentSymbol) return;
    
    setIsManaging(true);
    try {
      if (isInWatchlist(currentSymbol)) {
        await removeFromWatchlist(currentSymbol);
      } else {
        await addToWatchlist(currentSymbol);
      }
    } catch (error) {
      console.error('Watchlist operation failed:', error);
    } finally {
      setIsManaging(false);
    }
  };

  const getChangeColorClass = (change: number) => {
    return change >= 0 ? 'text-success' : 'text-error';
  };

  return (
    <div className="card-base p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-heading-sm flex items-center space-x-2">
          <DotIndicator category="navigation" size="sm" />
          <span>自选股</span>
        </h3>
        
        {currentSymbol && (
          <EnhancedButton
            variant={isInWatchlist(currentSymbol) ? "secondary" : "primary"}
            size="sm"
            onClick={handleWatchlistToggle}
            disabled={isManaging}
            className="flex items-center space-x-1"
          >
                         {isManaging ? (
               <CircularProgress progress={50} size={12} />
             ) : (
              <span>{isInWatchlist(currentSymbol) ? '−' : '+'}</span>
            )}
            <span className="text-xs">
              {isInWatchlist(currentSymbol) ? '移除' : '添加'}
            </span>
          </EnhancedButton>
        )}
      </div>

      {watchlist.length > 0 ? (
        <div className="space-y-3">
          {watchlist.map((item, index) => (
            <div
              key={index}
              onClick={() => onSymbolSelect(item.symbol)}
              className={`p-3 rounded-lg cursor-pointer transition-colors ${
                item.symbol === currentSymbol 
                  ? 'bg-brand-primary text-white' 
                  : 'bg-secondary hover:bg-tertiary'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <div className="font-semibold truncate">{item.symbol}</div>
                  <div className="text-xs opacity-75 truncate">{item.name}</div>
                </div>
                <div className="text-right ml-2">
                  <div className="text-sm">${item.current_price?.toFixed(2) || 'N/A'}</div>
                  <div className={`text-xs ${
                    item.symbol === currentSymbol 
                      ? 'opacity-75' 
                      : getChangeColorClass(item.change_percent || 0)
                  }`}>
                    {item.change_percent ? 
                      `${item.change_percent >= 0 ? '+' : ''}${item.change_percent.toFixed(2)}%` : 
                      'N/A'
                    }
                  </div>
                </div>
              </div>
              
              {item.alerts_enabled && (
                <div className="mt-2 pt-2 border-t border-current opacity-50">
                  <div className="text-xs">
                    {item.target_price && `目标: $${item.target_price.toFixed(2)}`}
                    {item.stop_loss && ` • 止损: $${item.stop_loss.toFixed(2)}`}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <DotIndicator category="navigation" size="lg" />
          <p className="mt-2 text-sm text-secondary">暂无自选股</p>
          <p className="text-xs text-tertiary mt-1">
            查询股票后点击添加按钮
          </p>
        </div>
      )}
    </div>
  );
};

export default DataQueryMode; 